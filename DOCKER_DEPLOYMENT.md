# Sec Flow Docker 部署方案

## 🎯 方案概述

本方案将前后端服务打包到同一个Docker容器内，通过Nginx反向代理实现统一访问入口。

### 架构设计

```
外部访问: example.com/sec-flow/*
    ↓
容器端口: 8080 (Nginx)
    ↓
内部路由分发:
├── /sec-flow/api/* → 后端服务 (localhost:8081)
└── /sec-flow/* → 前端静态文件
```

### 容器内部服务

- **Nginx**: 监听8080端口，负责路由分发
- **Go后端**: 监听8081端口，处理API请求
- **Supervisor**: 管理多进程服务

## 📁 文件结构

```
sec-flow/
├── Dockerfile                    # 多阶段构建文件
├── docker-compose.yml           # 容器编排配置
├── .dockerignore                # Docker忽略文件
├── docker/
│   ├── nginx.conf               # Nginx配置
│   ├── supervisord.conf         # Supervisor配置
│   ├── start.sh                 # 启动脚本
│   └── mysql/
│       └── init.sql             # MySQL初始化脚本
├── scripts/
│   ├── build.sh                 # 构建脚本
│   └── deploy.sh                # 部署脚本
└── server/conf/
    └── config-docker.yaml       # Docker环境配置
```

## 🚀 快速部署

### 1. 构建镜像

```bash
# 给脚本执行权限
chmod +x scripts/build.sh scripts/deploy.sh

# 构建镜像
./scripts/build.sh
```

### 2. 启动服务

```bash
# 部署服务
./scripts/deploy.sh

# 或者手动启动
docker-compose up -d
```

### 3. 验证服务

```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f sec-flow

# 健康检查
curl http://localhost:8080/health
```

## 🌐 访问地址

- **前端应用**: http://localhost:8080/sec-flow/
- **后端API**: http://localhost:8080/sec-flow/api/v1/
- **健康检查**: http://localhost:8080/health
- **HTTP触发器**: http://localhost:8080/api/execute/flow/{key}

## 🔧 配置说明

### Nginx配置要点

1. **路由分发**:
   - `/sec-flow/api/*` → 代理到后端服务
   - `/sec-flow/*` → 前端静态文件
   - `/health` → 后端健康检查
   - `/api/execute/*` → HTTP触发器

2. **静态资源优化**:
   - JS/CSS/图片等静态资源缓存1年
   - HTML文件不缓存
   - 启用Gzip压缩

3. **安全配置**:
   - 添加安全头
   - 超时设置
   - 缓冲配置

### 后端配置调整

- 监听端口改为8081
- 数据库主机改为docker-compose服务名
- 日志输出到stdout
- CORS允许所有来源

## 🔄 生产环境部署

### 1. 修改配置

```bash
# 修改docker-compose.yml中的环境变量
environment:
  - DB_HOST=your-mysql-host
  - DB_USERNAME=your-username
  - DB_PASSWORD=your-password
  - JWT_SECRET=your-jwt-secret
```

### 2. 外部数据库

如果使用外部数据库，可以移除docker-compose.yml中的mysql服务：

```yaml
# 注释或删除mysql服务
# mysql:
#   image: mysql:8.0
#   ...
```

### 3. 域名反向代理

在你的主域名nginx配置中添加：

```nginx
# example.com的nginx配置
location /sec-flow/ {
    proxy_pass http://your-container-host:8080/sec-flow/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 🛠️ 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f sec-flow
docker-compose logs -f mysql

# 重启服务
docker-compose restart sec-flow

# 停止服务
docker-compose down

# 清理数据（谨慎使用）
docker-compose down -v

# 进入容器
docker-compose exec sec-flow sh

# 查看nginx配置
docker-compose exec sec-flow cat /etc/nginx/nginx.conf

# 重载nginx配置
docker-compose exec sec-flow nginx -s reload
```

## 🔍 故障排查

### 1. 服务无法启动

```bash
# 查看详细日志
docker-compose logs sec-flow

# 检查端口占用
netstat -tlnp | grep 8080

# 检查镜像构建
docker images | grep sec-flow
```

### 2. 前端无法访问

```bash
# 检查nginx配置
docker-compose exec sec-flow nginx -t

# 查看nginx日志
docker-compose logs sec-flow | grep nginx
```

### 3. 后端API无法访问

```bash
# 检查后端服务状态
docker-compose exec sec-flow supervisorctl status

# 查看后端日志
docker-compose logs sec-flow | grep sec-flow-server
```

### 4. 数据库连接问题

```bash
# 检查数据库服务
docker-compose logs mysql

# 测试数据库连接
docker-compose exec mysql mysql -u sec_flow_user -p sec_flow
```

## 📝 注意事项

1. **端口冲突**: 确保8080和3306端口未被占用
2. **资源限制**: 根据服务器配置调整内存和CPU限制
3. **数据备份**: 生产环境请定期备份MySQL数据
4. **安全配置**: 修改默认密码和JWT密钥
5. **日志管理**: 配置日志轮转避免磁盘空间不足

## 🎉 总结

这个方案的优势：

- ✅ **单容器部署**: 简化部署和管理
- ✅ **统一入口**: 通过nginx统一处理请求
- ✅ **路径一致**: 前后端都使用/sec-flow前缀
- ✅ **易于扩展**: 可以轻松添加更多服务
- ✅ **生产就绪**: 包含完整的监控和日志配置
