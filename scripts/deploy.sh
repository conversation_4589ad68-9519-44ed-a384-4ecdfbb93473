#!/bin/bash

# Sec Flow 部署脚本

set -e

echo "🚀 开始部署 Sec Flow 应用..."

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down

# 清理旧镜像（可选）
read -p "是否清理旧镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
fi

# 构建新镜像
echo "🔨 构建新镜像..."
docker-compose build

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 健康检查
echo "🏥 执行健康检查..."
for i in {1..30}; do
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ 服务启动成功！"
        break
    fi
    echo "等待服务启动... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 部署完成！"
echo ""
echo "📊 服务访问地址："
echo "   前端: http://localhost:8080/sec-flow/"
echo "   后端健康检查: http://localhost:8080/health"
echo ""
echo "📝 查看日志："
echo "   docker-compose logs -f sec-flow"
echo "   docker-compose logs -f mysql"
