#!/bin/bash

# Sec Flow 构建脚本

set -e

echo "🚀 开始构建 Sec Flow 应用..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装，请先安装 docker-compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p docker/mysql

# 构建镜像
echo "🔨 构建 Docker 镜像..."
docker-compose build --no-cache

echo "✅ 构建完成！"
echo ""
echo "🎯 使用以下命令启动服务："
echo "   docker-compose up -d"
echo ""
echo "📊 服务访问地址："
echo "   前端: http://localhost:8080/sec-flow/"
echo "   后端健康检查: http://localhost:8080/health"
echo "   API文档: http://localhost:8080/sec-flow/api/v1"
