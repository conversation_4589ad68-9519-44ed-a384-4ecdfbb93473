# 多阶段构建 Dockerfile
# 阶段1: 构建前端
FROM node:20-alpine AS frontend-builder

WORKDIR /app/web

# 复制前端依赖文件
COPY web/package*.json ./
RUN npm ci --only=production

# 复制前端源码并构建
COPY web/ ./
RUN npm run build

# 阶段2: 构建后端
FROM golang:1.21-alpine AS backend-builder

WORKDIR /app/server

# 安装必要的包
RUN apk add --no-cache git

# 复制go mod文件
COPY server/go.mod server/go.sum ./
RUN go mod download

# 复制后端源码
COPY server/ ./

# 构建后端应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o sec-flow-server main.go

# 阶段3: 最终运行镜像
FROM nginx:alpine

# 安装supervisor用于管理多个进程
RUN apk add --no-cache supervisor

# 创建必要的目录
RUN mkdir -p /app/server /app/web /var/log/supervisor /etc/supervisor/conf.d

# 复制构建好的后端应用
COPY --from=backend-builder /app/server/sec-flow-server /app/server/
COPY --from=backend-builder /app/server/conf /app/server/conf/

# 复制构建好的前端静态文件
COPY --from=frontend-builder /app/web/dist /app/web/

# 复制nginx配置
COPY docker/nginx.conf /etc/nginx/nginx.conf

# 复制supervisor配置
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 复制启动脚本
COPY docker/start.sh /start.sh
RUN chmod +x /start.sh

# 暴露端口
EXPOSE 8080

# 设置工作目录
WORKDIR /app

# 启动supervisor
CMD ["/start.sh"]
