[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/nginx.err.log
stdout_logfile=/var/log/supervisor/nginx.out.log
priority=10

[program:sec-flow-server]
command=/app/server/sec-flow-server
directory=/app/server
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/sec-flow-server.err.log
stdout_logfile=/var/log/supervisor/sec-flow-server.out.log
environment=SERVER_PORT=8081,GIN_MODE=release
priority=20

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
