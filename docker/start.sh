#!/bin/sh

echo "🚀 Starting Sec Flow Container..."

# 检查必要的文件是否存在
if [ ! -f "/app/server/sec-flow-server" ]; then
    echo "❌ Backend binary not found!"
    exit 1
fi

if [ ! -d "/app/web" ]; then
    echo "❌ Frontend files not found!"
    exit 1
fi

# 设置权限
chmod +x /app/server/sec-flow-server

# 创建日志目录
mkdir -p /var/log/supervisor
mkdir -p /var/log/nginx

echo "✅ All checks passed, starting services..."

# 启动supervisor
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
