version: '3.8'

services:
  sec-flow:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sec-flow-app
    ports:
      - "8080:8080"
    environment:
      - SERVER_PORT=8081
      - GIN_MODE=release
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=sec_flow_user
      - DB_PASSWORD=sec_flow_password
      - DB_DATABASE=sec_flow
    volumes:
      - ./logs:/var/log/supervisor
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - sec-flow-network

  mysql:
    image: mysql:8.0
    container_name: sec-flow-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=sec_flow
      - MYSQL_USER=sec_flow_user
      - MYSQL_PASSWORD=sec_flow_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    restart: unless-stopped
    networks:
      - sec-flow-network

volumes:
  mysql_data:

networks:
  sec-flow-network:
    driver: bridge
