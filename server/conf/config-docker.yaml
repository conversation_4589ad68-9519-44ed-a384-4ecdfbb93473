# Docker环境配置
app:
  name: "Sec Flow Server"
  version: "1.0.0"
  env: "docker"
  debug: false

server:
  host: "0.0.0.0"
  port: 8081  # 容器内部端口，nginx监听8080
  read_timeout: 60s
  write_timeout: 60s
  idle_timeout: 60s

database:
  driver: "mysql"
  host: "mysql"  # docker-compose服务名
  port: 3306
  username: "sec_flow_user"
  password: "sec_flow_password"
  database: "sec_flow"
  charset: "utf8mb4"
  parse_time: true
  loc: "Local"
  max_idle_conns: 25
  max_open_conns: 250
  conn_max_lifetime: 3600s
  log_level: "error"

log:
  level: "info"
  format: "json"
  output: "stdout"  # 容器环境输出到stdout
  file_path: "/var/log/sec-flow/app.log"
  max_size: 100
  max_backups: 7
  max_age: 30
  compress: true

scheduler:
  heartbeat_interval: 30s
  heartbeat_timeout: 90s
  active_instance_timeout: 60s
  preprocess_lock_timeout: 90s
  task_assignment_timeout: 180s

cors:
  allow_origins:
    - "*"  # 容器环境允许所有来源
  allow_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allow_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true

jwt:
  secret: "docker-jwt-secret-key-change-in-production"

third:
  lark:
    app_id: "cli_9f8fe851c0b4d00e"
    app_secret: "0lBvsLbdGP52QBASDDFGpgbvlkAYkuGp"
  mongo:
    address: "dds-2zeb56a5379411a42.mongodb.rds.aliyuncs.com:3717,dds-2zeb56a5379411a41.mongodb.rds.aliyuncs.com:3717"
    username: "root"
    password: "q4geigPgwFTqLePB3Ze3"
